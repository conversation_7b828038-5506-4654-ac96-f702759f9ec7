<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LanguageSelector 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .test-result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
        }
        .error {
            color: #d32f2f;
            background: #ffebee;
        }
        .success {
            color: #2e7d32;
            background: #e8f5e9;
        }
        .warning {
            color: #f57c00;
            background: #fff3e0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>LanguageSelector 组件修复验证</h1>
        
        <div class="test-section">
            <div class="test-title">修复内容总结</div>
            <ul>
                <li><strong>问题1</strong>: LanguageSelector组件在页面初始化时立即加载</li>
                <li><strong>问题2</strong>: useEffect第55行调用onLanguageChange时可能为undefined</li>
                <li><strong>问题3</strong>: 组件初始化时机导致的时序问题</li>
                <li><strong>问题4</strong>: 缺少错误处理和防护机制</li>
            </ul>
        </div>

        <div class="test-section">
            <div class="test-title">修复方案</div>
            <div class="test-result success">
                <strong>✅ 修复1: 添加函数类型检查</strong><br>
                在调用onLanguageChange前检查其是否为有效函数<br><br>
                
                <strong>✅ 修复2: 添加初始化标志</strong><br>
                使用isInitialized状态避免初始渲染时的不必要调用<br><br>
                
                <strong>✅ 修复3: 优化依赖数组</strong><br>
                在useEffect依赖数组中添加onLanguageChange<br><br>
                
                <strong>✅ 修复4: 使用useCallback优化</strong><br>
                在AIProcessModal中使用useCallback优化handleLanguageChange<br><br>
                
                <strong>✅ 修复5: 参数验证</strong><br>
                在WebAssistantManager中添加参数验证和错误处理<br><br>
                
                <strong>✅ 修复6: 接口优化</strong><br>
                将onLanguageChange改为可选参数，提高组件健壮性
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">修复后的代码流程</div>
            <div class="test-result">
                <strong>1. 组件初始化阶段:</strong><br>
                - LanguageSelector组件挂载<br>
                - isInitialized设置为false<br>
                - 第一个useEffect设置isInitialized为true<br><br>
                
                <strong>2. 语言变更检测:</strong><br>
                - 检查isInitialized是否为true<br>
                - 检查onLanguageChange是否为有效函数<br>
                - 安全调用回调函数<br><br>
                
                <strong>3. 错误处理:</strong><br>
                - 无效回调时输出警告而不是抛出错误<br>
                - 参数验证确保数据完整性<br>
                - 优雅降级处理异常情况
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">预期效果</div>
            <div class="test-result success">
                <strong>✅ 不再出现"变量或方法不存在"错误</strong><br>
                <strong>✅ 组件初始化时不会立即触发不必要的回调</strong><br>
                <strong>✅ 语言选择变更时能正确触发翻译</strong><br>
                <strong>✅ 提供更好的错误提示和调试信息</strong><br>
                <strong>✅ 提高组件的健壮性和可维护性</strong>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">建议的测试步骤</div>
            <ol>
                <li>重新加载Chrome扩展</li>
                <li>在网页上选择文本并点击翻译</li>
                <li>观察控制台是否还有错误信息</li>
                <li>尝试更改语言选择，确认翻译能正确重新触发</li>
                <li>测试多次快速切换语言选择的情况</li>
            </ol>
        </div>
    </div>

    <script>
        console.log('LanguageSelector修复验证页面已加载');
        console.log('请按照测试步骤验证修复效果');
    </script>
</body>
</html>
